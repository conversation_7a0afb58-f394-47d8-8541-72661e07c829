/** @format */

'use client';

import { useEffect, useRef, useState } from 'react';

// Animation types for different section transitions
export type AnimationType = 
	| 'wave-wash' 
	| 'slide-diagonal' 
	| 'fade-scale' 
	| 'ripple-effect'
	| 'geometric-morph';

// Configuration for scroll animations
export interface ScrollAnimationConfig {
	threshold: number;
	rootMargin: string;
	animationType: AnimationType;
	duration: number;
	delay: number;
	easing: string;
}

// Default animation configurations
export const ANIMATION_CONFIGS: Record<string, ScrollAnimationConfig> = {
	heroToFeatures: {
		threshold: 0.3,
		rootMargin: '-10% 0px -10% 0px',
		animationType: 'wave-wash',
		duration: 1200,
		delay: 0,
		easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
	},
	featureToFeature: {
		threshold: 0.4,
		rootMargin: '-5% 0px -5% 0px',
		animationType: 'slide-diagonal',
		duration: 800,
		delay: 100,
		easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
	},
	featureToFooter: {
		threshold: 0.2,
		rootMargin: '-15% 0px -15% 0px',
		animationType: 'fade-scale',
		duration: 1000,
		delay: 200,
		easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
	},
};

// Hook for scroll-triggered animations
export const useScrollAnimation = (
	config: ScrollAnimationConfig,
	triggerOnce: boolean = true
) => {
	const elementRef = useRef<HTMLElement>(null);
	const [isVisible, setIsVisible] = useState(false);
	const [hasTriggered, setHasTriggered] = useState(false);

	useEffect(() => {
		const element = elementRef.current;
		if (!element) return;

		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						if (!triggerOnce || !hasTriggered) {
							setIsVisible(true);
							setHasTriggered(true);
						}
					} else if (!triggerOnce) {
						setIsVisible(false);
					}
				});
			},
			{
				threshold: config.threshold,
				rootMargin: config.rootMargin,
			}
		);

		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [config.threshold, config.rootMargin, triggerOnce, hasTriggered]);

	return { elementRef, isVisible, hasTriggered };
};

// Hook for section transition animations
export const useSectionTransition = (
	sectionId: string,
	animationType: AnimationType = 'slide-diagonal'
) => {
	const config = ANIMATION_CONFIGS.featureToFeature;
	config.animationType = animationType;
	
	const { elementRef, isVisible } = useScrollAnimation(config);
	const [animationState, setAnimationState] = useState<'idle' | 'entering' | 'visible'>('idle');

	useEffect(() => {
		if (isVisible && animationState === 'idle') {
			setAnimationState('entering');
			setTimeout(() => {
				setAnimationState('visible');
			}, config.duration);
		}
	}, [isVisible, animationState, config.duration]);

	return { elementRef, animationState, isVisible };
};

// CSS animation classes generator
export const getAnimationClasses = (
	animationType: AnimationType,
	state: 'idle' | 'entering' | 'visible'
): string => {
	const baseClasses = 'transition-all duration-1000 ease-out';
	
	switch (animationType) {
		case 'wave-wash':
			return `${baseClasses} ${
				state === 'idle' 
					? 'opacity-0 transform translate-y-8 scale-95' 
					: state === 'entering'
					? 'opacity-70 transform translate-y-4 scale-98'
					: 'opacity-100 transform translate-y-0 scale-100'
			}`;
			
		case 'slide-diagonal':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform translate-x-12 translate-y-8 rotate-1'
					: state === 'entering'
					? 'opacity-70 transform translate-x-6 translate-y-4 rotate-0.5'
					: 'opacity-100 transform translate-x-0 translate-y-0 rotate-0'
			}`;
			
		case 'fade-scale':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform scale-90'
					: state === 'entering'
					? 'opacity-50 transform scale-95'
					: 'opacity-100 transform scale-100'
			}`;
			
		case 'ripple-effect':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform scale-110'
					: state === 'entering'
					? 'opacity-70 transform scale-105'
					: 'opacity-100 transform scale-100'
			}`;
			
		case 'geometric-morph':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform skew-x-3 skew-y-1 scale-95'
					: state === 'entering'
					? 'opacity-70 transform skew-x-1 skew-y-0.5 scale-98'
					: 'opacity-100 transform skew-x-0 skew-y-0 scale-100'
			}`;
			
		default:
			return baseClasses;
	}
};

// Wave animation component for section transitions
export const WaveTransition: React.FC<{
	isActive: boolean;
	direction: 'up' | 'down';
	color?: string;
}> = ({ isActive, direction, color = '#3B82F6' }) => {
	return (
		<div 
			className={`absolute inset-x-0 pointer-events-none transition-all duration-1200 ease-out ${
				direction === 'up' ? 'bottom-0' : 'top-0'
			}`}
			style={{
				height: isActive ? '100px' : '0px',
				opacity: isActive ? 1 : 0,
				transform: isActive 
					? 'translateY(0)' 
					: direction === 'up' 
					? 'translateY(100px)' 
					: 'translateY(-100px)',
			}}
		>
			<svg
				className="w-full h-full"
				viewBox="0 0 1200 120"
				preserveAspectRatio="none"
			>
				<path
					d={direction === 'up' 
						? "M0,120 C150,100 350,0 600,10 C850,20 1050,100 1200,80 L1200,120 Z"
						: "M0,0 C150,20 350,120 600,110 C850,100 1050,20 1200,40 L1200,0 Z"
					}
					fill={color}
					fillOpacity="0.3"
				/>
				<path
					d={direction === 'up'
						? "M0,120 C300,80 600,20 1200,60 L1200,120 Z"
						: "M0,0 C300,40 600,100 1200,60 L1200,0 Z"
					}
					fill={color}
					fillOpacity="0.5"
				/>
			</svg>
		</div>
	);
};

// Geometric shapes for background animations
export const AnimatedGeometry: React.FC<{
	isVisible: boolean;
	shapes: Array<{
		type: 'circle' | 'triangle' | 'square';
		size: number;
		x: number;
		y: number;
		color: string;
		delay: number;
	}>;
}> = ({ isVisible, shapes }) => {
	return (
		<div className="absolute inset-0 pointer-events-none overflow-hidden">
			{shapes.map((shape, index) => (
				<div
					key={index}
					className="absolute transition-all duration-1000 ease-out"
					style={{
						left: `${shape.x}%`,
						top: `${shape.y}%`,
						width: `${shape.size}px`,
						height: `${shape.size}px`,
						backgroundColor: shape.color,
						opacity: isVisible ? 0.6 : 0,
						transform: isVisible 
							? 'scale(1) rotate(0deg)' 
							: 'scale(0) rotate(45deg)',
						transitionDelay: `${shape.delay}ms`,
						borderRadius: shape.type === 'circle' ? '50%' : '0%',
						clipPath: shape.type === 'triangle' 
							? 'polygon(50% 0%, 0% 100%, 100% 100%)' 
							: 'none',
					}}
				/>
			))}
		</div>
	);
};
