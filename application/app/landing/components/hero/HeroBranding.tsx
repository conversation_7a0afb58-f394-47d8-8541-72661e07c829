/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LANDING_PAGE_DATA,
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';

interface HeroBrandingProps {
	onGetStarted: () => void;
}

const HeroBranding: React.FC<HeroBrandingProps> = ({ onGetStarted }) => {
	// Get real data counts
	const categoriesCount = getPOICategories().length;
	const subcategoriesCount = getPOISubcategories().length;
	const [isHovered, setIsHovered] = useState(false);
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);

	// Detect screen size for responsive behavior
	useEffect(() => {
		const updateScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		updateScreenSize();
		window.addEventListener('resize', updateScreenSize);
		return () => window.removeEventListener('resize', updateScreenSize);
	}, []);

	return (
		<div className='relative w-full max-w-4xl mx-auto'>
			{/* Modern Brand-Focused Hero */}
			<div
				className='relative overflow-hidden transition-all duration-700'
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}>
				{/* Animated Background Elements */}
				<div className='absolute inset-0 opacity-20'>
					{/* Floating geometric shapes */}
					<div
						className='absolute top-10 left-10 w-20 h-20 rounded-full'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.blue}40 0%, transparent 70%)`,
							transform: isHovered
								? 'scale(1.2) translateY(-10px)'
								: 'scale(1)',
							transition: 'transform 0.8s ease-out',
						}}
					/>
					<div
						className='absolute top-32 right-16 w-16 h-16 rounded-lg rotate-45'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.green}30 0%, transparent 70%)`,
							transform: isHovered
								? 'scale(1.3) rotate(90deg)'
								: 'scale(1) rotate(45deg)',
							transition: 'transform 0.8s ease-out',
						}}
					/>
					<div
						className='absolute bottom-20 left-20 w-12 h-12 rounded-full'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.navy}50 0%, transparent 70%)`,
							transform: isHovered ? 'scale(1.4) translateX(20px)' : 'scale(1)',
							transition: 'transform 0.8s ease-out',
						}}
					/>
				</div>

				{/* Main Content */}
				<div className='relative z-10 text-center space-y-8 py-12 px-6'>
					{/* Logo Section */}
					<div className='flex justify-center mb-8'>
						<div
							className='relative group'
							style={{
								transform: isHovered
									? 'translateY(-10px) scale(1.05)'
									: 'translateY(0) scale(1)',
								transition: 'transform 0.6s ease-out',
							}}>
							<img
								src='/logo/512x512.png'
								alt='Wizlop Logo'
								className='w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 object-contain'
								style={{
									filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
								}}
							/>
							{/* Glowing ring effect */}
							<div
								className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500'
								style={{
									background: `conic-gradient(from 0deg, ${colors.brand.blue}40, ${colors.brand.green}40, ${colors.brand.blue}40)`,
									filter: 'blur(8px)',
									transform: 'scale(1.2)',
								}}
							/>
						</div>
					</div>

					{/* Brand Name */}
					<div className='space-y-4'>
						<h1
							className='font-black tracking-tight leading-none'
							style={{
								fontSize:
									screenSize === 'mobile'
										? '3.5rem'
										: screenSize === 'tablet'
										? '4.5rem'
										: '5.5rem',
							}}>
							<span
								className='bg-clip-text text-transparent block'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`,
									WebkitBackgroundClip: 'text',
									WebkitTextFillColor: 'transparent',
									transform: isHovered ? 'scale(1.02)' : 'scale(1)',
									transition: 'transform 0.5s ease-out',
								}}>
								{LANDING_PAGE_DATA.branding.companyName}
							</span>
						</h1>

						{/* Tagline with enhanced styling */}
						<div className='relative'>
							<p
								className='font-semibold tracking-wide text-lg sm:text-xl lg:text-2xl'
								style={{
									color: colors.brand.blue,
									transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
									transition: 'transform 0.5s ease-out',
								}}>
								{LANDING_PAGE_DATA.branding.tagline}
							</p>
							{/* Animated underline */}
							<div
								className='absolute -bottom-2 left-1/2 transform -translate-x-1/2 rounded-full transition-all duration-700'
								style={{
									background: `linear-gradient(90deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
									width: isHovered ? '100%' : '60%',
									height: '3px',
								}}
							/>
						</div>
					</div>

					{/* Description */}
					<div className='max-w-2xl mx-auto space-y-6'>
						<p
							className='text-lg sm:text-xl lg:text-2xl leading-relaxed font-medium'
							style={{
								color: colors.neutral.slateGray,
								transform: isHovered ? 'translateY(-1px)' : 'translateY(0)',
								transition: 'transform 0.5s ease-out',
							}}>
							{LANDING_PAGE_DATA.branding.heroDescription}
						</p>
					</div>

					{/* Call to Action */}
					<div className='space-y-6'>
						<button
							onClick={onGetStarted}
							className='group relative px-8 py-4 sm:px-12 sm:py-5 rounded-full font-bold text-lg sm:text-xl transition-all duration-500 transform hover:scale-105 active:scale-95'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
								color: 'white',
								boxShadow: isHovered
									? `0 20px 40px rgba(51, 194, 255, 0.4)`
									: '0 10px 25px rgba(51, 194, 255, 0.2)',
							}}>
							<span className='relative z-10'>Start Exploring</span>
							{/* Animated background */}
							<div
								className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
								}}
							/>
							{/* Ripple effect */}
							<div
								className='absolute inset-0 rounded-full opacity-0 group-active:opacity-100 transition-opacity duration-200'
								style={{
									background: `radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)`,
								}}
							/>
						</button>

						{/* Subtle stats */}
						<div className='flex items-center justify-center space-x-8 text-sm opacity-75'>
							<div className='text-center'>
								<div
									className='font-bold text-lg'
									style={{ color: colors.brand.blue }}>
									{categoriesCount}
								</div>
								<div style={{ color: colors.neutral.slateGray }}>
									Categories
								</div>
							</div>
							<div
								className='w-px h-8'
								style={{ background: colors.ui.gray200 }}
							/>
							<div className='text-center'>
								<div
									className='font-bold text-lg'
									style={{ color: colors.brand.green }}>
									{subcategoriesCount}
								</div>
								<div style={{ color: colors.neutral.slateGray }}>Locations</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroBranding;
