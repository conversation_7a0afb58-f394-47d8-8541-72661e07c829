
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.4 MiB [emitted] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 107 KiB
  modules by path ./app/landing/ 90.5 KiB
    modules by path ./app/landing/components/ 68.4 KiB 9 modules
    modules by path ./app/landing/utils/*.ts 22.1 KiB
      ./app/landing/utils/dynamicLayout.ts 6.69 KiB [built] [code generated] [23 errors]
      ./app/landing/utils/scrollAnimations.ts 6.54 KiB [built] [code generated] [87 errors]
      ./app/landing/utils/responsiveUtils.ts 8.85 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 12.1 KiB [built] [code generated]

ERROR in ./app/landing/utils/dynamicLayout.ts 95:12
Module parse failed: Unexpected token (95:12)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
| style = {};
| {
>     styles, ;
|     style;
| }
ModuleParseError: Module parse failed: Unexpected token (95:12)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
| style = {};
| {
>     styles, ;
|     style;
| }
    at handleParseError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1211:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1354:5
    at processResult (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:974:11)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1072:5
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:407:3
    at iterateNormalLoaders (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:233:10)
    at iterateNormalLoaders (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:240:10)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:255:3
    at context.callback (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:124:13)
    at makeSourceMapAndFinish (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/index.js:68:9)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in ./app/landing/utils/scrollAnimations.ts 132:4
Module parse failed: Unexpected token (132:4)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|             ? 'translateY(100px)'
|             : 'translateY(-100px)',
>     ;
| }
|     >
ModuleParseError: Module parse failed: Unexpected token (132:4)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|             ? 'translateY(100px)'
|             : 'translateY(-100px)',
>     ;
| }
|     >
    at handleParseError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1211:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1354:5
    at processResult (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:974:11)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/NormalModule.js:1072:5
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:407:3
    at iterateNormalLoaders (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:233:10)
    at iterateNormalLoaders (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:240:10)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:255:3
    at context.callback (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/loader-runner/lib/LoaderRunner.js:124:13)
    at makeSourceMapAndFinish (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/index.js:68:9)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx
./app/landing/components/LandingPage.tsx 44:9-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(44,10)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(44,10)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx
./app/landing/components/LandingPage.tsx 91:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(91,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(91,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx
./app/landing/components/LandingPage.tsx 149:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(149,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(149,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx
./app/landing/components/LandingPage.tsx 173:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(173,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/LandingPage.tsx(173,6)
      TS2322: Type 'RefObject<HTMLElement>' is not assignable to type 'LegacyRef<HTMLDivElement> | undefined'.
  Type 'RefObject<HTMLElement>' is not assignable to type 'RefObject<HTMLDivElement>'.
    Property 'align' is missing in type 'HTMLElement' but required in type 'HTMLDivElement'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 153:3-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,4)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,4)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 153:12-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,13)
      TS1005: ')' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,13)
      TS1005: ')' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 153:14-17
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,15)
      TS1136: Property assignment expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,15)
      TS1136: Property assignment expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:10-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,11)
      TS1136: Property assignment expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,11)
      TS1136: Property assignment expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:12-15
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,13)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,13)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:23-26
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,24)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,24)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:33-34
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,34)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,34)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:34-35
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,35)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,35)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 156:3-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(156,4)
      TS1161: Unterminated regular expression literal.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(156,4)
      TS1161: Unterminated regular expression literal.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 157:1-2
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(157,2)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(157,2)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 158:0-1
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(158,1)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(158,1)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 39:2-10
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(39,3)
      TS2322: Type 'string' is not assignable to type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(39,3)
      TS2322: Type 'string' is not assignable to type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 46:32-40
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(46,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(46,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 47:32-40
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(47,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(47,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 48:33-41
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(48,34)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(48,34)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 152:3-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(152,4)
      TS2304: Cannot find name 'div'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(152,4)
      TS2304: Cannot find name 'div'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 153:13-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,14)
      TS2349: This expression is not callable.
  Type '{}' has no call signatures.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(153,14)
      TS2349: This expression is not callable.
  Type '{}' has no call signatures.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:3-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,4)
      TS2304: Cannot find name 'style'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,4)
      TS2304: Cannot find name 'style'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:15-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,16)
      TS2304: Cannot find name 'styles'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,16)
      TS2304: Cannot find name 'styles'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:15-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,16)
      TS2695: Left side of comma operator is unused and has no side effects.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,16)
      TS2695: Left side of comma operator is unused and has no side effects.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:26-31
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,27)
      TS2304: Cannot find name 'style'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,27)
      TS2304: Cannot find name 'style'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 154:34-156:8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,35)
      TS2365: Operator '<' cannot be applied to types 'boolean' and 'RegExp'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(154,35)
      TS2365: Operator '<' cannot be applied to types 'boolean' and 'RegExp'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 155:4-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(155,5)
      TS18004: No value exists in scope for the shorthand property 'children'. Either declare one or provide an initializer.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(155,5)
      TS18004: No value exists in scope for the shorthand property 'children'. Either declare one or provide an initializer.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 4:0-65 27:27-50 52:39-62 81:39-62 85:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 184:3-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,4)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,4)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 184:12-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,13)
      TS1005: ')' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,13)
      TS1005: ')' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 184:14-94
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,15)
      TS1136: Property assignment expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,15)
      TS1136: Property assignment expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 187:10-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(187,11)
      TS1136: Property assignment expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(187,11)
      TS1136: Property assignment expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 189:11-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,12)
      TS1005: ';' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,12)
      TS1005: ';' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 190:13-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,14)
      TS1005: ';' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,14)
      TS1005: ';' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 195:3-4
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(195,4)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(195,4)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 195:4-5
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(195,5)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(195,5)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 196:2-3
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(196,3)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(196,3)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 198:4-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,5)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,5)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 198:13-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,14)
      TS1005: ';' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,14)
      TS1005: ';' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 203:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,6)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,6)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 203:6-7
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,7)
      TS1005: ';' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,7)
      TS1005: ';' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 209:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(209,6)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(209,6)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 211:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,6)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,6)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 211:6-7
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,7)
      TS1005: ';' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,7)
      TS1005: ';' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 217:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(217,6)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(217,6)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 218:4-5
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(218,5)
      TS1110: Type expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(218,5)
      TS1110: Type expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 219:3-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(219,4)
      TS1161: Unterminated regular expression literal.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(219,4)
      TS1161: Unterminated regular expression literal.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 220:1-2
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(220,2)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(220,2)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 221:0-1
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(221,1)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(221,1)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 236:7-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,8)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,8)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 236:16-17
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,17)
      TS1005: ')' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,17)
      TS1005: ')' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 237:10-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,11)
      TS1005: ',' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,11)
      TS1005: ',' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 239:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,6)
      TS1005: '>' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,6)
      TS1005: '>' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 239:8-9
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,9)
      TS1005: ')' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,9)
      TS1005: ')' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 240:5-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(240,6)
      TS1005: ',' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(240,6)
      TS1005: ',' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 241:5-10
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,6)
      TS1005: ',' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,6)
      TS1005: ',' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 241:12-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,13)
      TS1136: Property assignment expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,13)
      TS1136: Property assignment expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 256:6-7
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(256,7)
      TS1005: ',' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(256,7)
      TS1005: ',' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 257:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(257,6)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(257,6)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 258:3-4
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,4)
      TS1109: Expression expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,4)
      TS1109: Expression expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 258:4-5
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,5)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,5)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 258:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,6)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(258,6)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 259:3-4
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(259,4)
      TS1110: Type expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(259,4)
      TS1110: Type expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 260:1-2
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(260,2)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(260,2)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 261:0-1
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(261,1)
      TS1128: Declaration or statement expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(261,1)
      TS1128: Declaration or statement expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 183:3-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(183,4)
      TS2304: Cannot find name 'div'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(183,4)
      TS2304: Cannot find name 'div'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 184:3-12
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,4)
      TS2304: Cannot find name 'className'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,4)
      TS2304: Cannot find name 'className'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 184:13-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,14)
      TS2349: This expression is not callable.
  Type '{}' has no call signatures.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(184,14)
      TS2349: This expression is not callable.
  Type '{}' has no call signatures.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 187:3-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(187,4)
      TS2304: Cannot find name 'style'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(187,4)
      TS2304: Cannot find name 'style'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 188:12-20
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(188,13)
      TS2304: Cannot find name 'isActive'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(188,13)
      TS2304: Cannot find name 'isActive'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 188:12-38
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(188,13)
      TS2695: Left side of comma operator is unused and has no side effects.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(188,13)
      TS2695: Left side of comma operator is unused and has no side effects.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 189:4-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,5)
      TS2304: Cannot find name 'opacity'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,5)
      TS2304: Cannot find name 'opacity'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 189:13-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,14)
      TS2304: Cannot find name 'isActive'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,14)
      TS2304: Cannot find name 'isActive'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 189:13-29
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,14)
      TS2695: Left side of comma operator is unused and has no side effects.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(189,14)
      TS2695: Left side of comma operator is unused and has no side effects.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 190:4-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,5)
      TS2304: Cannot find name 'transform'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,5)
      TS2304: Cannot find name 'transform'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 190:15-23
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,16)
      TS2304: Cannot find name 'isActive'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,16)
      TS2304: Cannot find name 'isActive'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 190:15-194:27
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,16)
      TS2695: Left side of comma operator is unused and has no side effects.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(190,16)
      TS2695: Left side of comma operator is unused and has no side effects.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 192:7-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(192,8)
      TS2304: Cannot find name 'direction'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(192,8)
      TS2304: Cannot find name 'direction'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 197:4-7
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(197,5)
      TS2304: Cannot find name 'svg'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(197,5)
      TS2304: Cannot find name 'svg'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 198:4-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,5)
      TS2304: Cannot find name 'className'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(198,5)
      TS2304: Cannot find name 'className'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 199:4-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(199,5)
      TS2304: Cannot find name 'viewBox'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(199,5)
      TS2304: Cannot find name 'viewBox'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 200:4-23
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(200,5)
      TS2304: Cannot find name 'preserveAspectRatio'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(200,5)
      TS2304: Cannot find name 'preserveAspectRatio'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 202:5-9
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(202,6)
      TS2304: Cannot find name 'path'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(202,6)
      TS2304: Cannot find name 'path'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 203:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,6)
      TS2304: Cannot find name 'd'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,6)
      TS2304: Cannot find name 'd'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 203:8-17
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,9)
      TS2304: Cannot find name 'direction'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(203,9)
      TS2304: Cannot find name 'direction'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 207:5-9
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(207,6)
      TS2304: Cannot find name 'fill'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(207,6)
      TS2304: Cannot find name 'fill'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 207:11-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(207,12)
      TS18004: No value exists in scope for the shorthand property 'color'. Either declare one or provide an initializer.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(207,12)
      TS18004: No value exists in scope for the shorthand property 'color'. Either declare one or provide an initializer.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 208:5-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(208,6)
      TS2304: Cannot find name 'fillOpacity'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(208,6)
      TS2304: Cannot find name 'fillOpacity'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 208:17-22
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(208,18)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(208,18)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 210:5-9
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(210,6)
      TS2304: Cannot find name 'path'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(210,6)
      TS2304: Cannot find name 'path'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 211:5-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,6)
      TS2304: Cannot find name 'd'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,6)
      TS2304: Cannot find name 'd'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 211:8-17
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,9)
      TS2304: Cannot find name 'direction'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(211,9)
      TS2304: Cannot find name 'direction'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 215:5-9
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(215,6)
      TS2304: Cannot find name 'fill'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(215,6)
      TS2304: Cannot find name 'fill'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 215:11-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(215,12)
      TS18004: No value exists in scope for the shorthand property 'color'. Either declare one or provide an initializer.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(215,12)
      TS18004: No value exists in scope for the shorthand property 'color'. Either declare one or provide an initializer.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 216:5-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,6)
      TS2304: Cannot find name 'fillOpacity'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,6)
      TS2304: Cannot find name 'fillOpacity'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 216:17-22
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,18)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,18)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 216:17-219:8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,18)
      TS2365: Operator '<' cannot be applied to types 'boolean' and 'RegExp'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(216,18)
      TS2365: Operator '<' cannot be applied to types 'boolean' and 'RegExp'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 236:3-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,4)
      TS2304: Cannot find name 'div'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,4)
      TS2304: Cannot find name 'div'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 236:7-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,8)
      TS2304: Cannot find name 'className'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,8)
      TS2304: Cannot find name 'className'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 236:17-257:5
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,18)
      TS2365: Operator '>' cannot be applied to types 'string' and 'number'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(236,18)
      TS2365: Operator '>' cannot be applied to types 'string' and 'number'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 237:3-256:7
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,4)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,4)
      TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 237:16-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,17)
      TS7006: Parameter 'shape' implicitly has an 'any' type.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,17)
      TS7006: Parameter 'shape' implicitly has an 'any' type.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 237:23-28
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,24)
      TS7006: Parameter 'index' implicitly has an 'any' type.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(237,24)
      TS7006: Parameter 'index' implicitly has an 'any' type.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 238:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(238,6)
      TS2304: Cannot find name 'div'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(238,6)
      TS2304: Cannot find name 'div'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 239:5-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,6)
      TS2304: Cannot find name 'key'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(239,6)
      TS2304: Cannot find name 'key'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 240:5-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(240,6)
      TS2304: Cannot find name 'className'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(240,6)
      TS2304: Cannot find name 'className'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 241:5-10
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,6)
      TS2304: Cannot find name 'style'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(241,6)
      TS2304: Cannot find name 'style'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 242:15-20
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(242,16)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(242,16)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 243:14-19
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(243,15)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(243,15)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 244:16-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(244,17)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(244,17)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 245:17-22
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(245,18)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(245,18)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 246:23-28
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(246,24)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(246,24)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 251:26-31
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(251,27)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(251,27)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 252:20-25
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(252,21)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(252,21)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts
./app/landing/utils/scrollAnimations.ts 253:16-21
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(253,17)
      TS2304: Cannot find name 'shape'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/scrollAnimations.ts(253,17)
      TS2304: Cannot find name 'shape'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 5:0-100 12:27-47 13:31-51 14:25-45 15:29-49 27:252-266 27:363-379 56:260-274 56:364-380 85:199-213
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

webpack 5.100.1 compiled with 116 errors in 3519 ms
