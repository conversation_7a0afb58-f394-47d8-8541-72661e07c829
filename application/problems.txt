
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.46 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 109 KiB
  modules by path ./app/landing/components/ 72 KiB 12 modules
  modules by path ./app/landing/utils/*.ts 20 KiB
    ./app/landing/utils/scrollAnimations.ts 4.46 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 8.85 KiB [built] [code generated]
    ./app/landing/utils/dynamicLayout.ts 6.66 KiB [built] [code generated] [4 errors]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 12.1 KiB [built] [code generated]

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 39:2-10
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(39,3)
      TS2322: Type 'string' is not assignable to type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(39,3)
      TS2322: Type 'string' is not assignable to type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/DynamicSectionContainer.tsx 4:0-64 6:18-40
 @ ./app/landing/components/LandingPage.tsx 6:0-64 41:39-62 66:39-62 95:39-62 99:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 46:32-40
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(46,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(46,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/DynamicSectionContainer.tsx 4:0-64 6:18-40
 @ ./app/landing/components/LandingPage.tsx 6:0-64 41:39-62 66:39-62 95:39-62 99:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 47:32-40
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(47,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(47,33)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/DynamicSectionContainer.tsx 4:0-64 6:18-40
 @ ./app/landing/components/LandingPage.tsx 6:0-64 41:39-62 66:39-62 95:39-62 99:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts
./app/landing/utils/dynamicLayout.ts 48:33-41
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(48,34)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/utils/dynamicLayout.ts(48,34)
      TS2345: Argument of type 'string' is not assignable to parameter of type 'SectionPosition'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/DynamicSectionContainer.tsx 4:0-64 6:18-40
 @ ./app/landing/components/LandingPage.tsx 6:0-64 41:39-62 66:39-62 95:39-62 99:311-334
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

webpack 5.100.1 compiled with 4 errors in 3533 ms
